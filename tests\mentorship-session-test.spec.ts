import { test, expect } from '@playwright/test';

test.describe('Mentorship Session Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5174');
  });

  test('should display mentorship dashboard correctly', async ({ page }) => {
    // Navigate directly to mentorship dashboard
    await page.goto('http://localhost:5174/dashboard/mentorship');
    
    // Check if the page loads
    await expect(page.locator('text=Mentorship')).toBeVisible();
    
    // Check if the main sections are visible
    await expect(page.locator('text=Pending Requests')).toBeVisible();
    await expect(page.locator('text=Active Mentorships')).toBeVisible();
    await expect(page.locator('text=Upcoming Sessions')).toBeVisible();
    await expect(page.locator('text=Today\'s Sessions')).toBeVisible();
  });

  test('should show schedule session button for accepted requests', async ({ page }) => {
    await page.goto('http://localhost:5174/dashboard/mentorship');
    
    // Look for accepted requests with schedule session button
    const scheduleButton = page.locator('text=SCHEDULE SESSION');
    
    // Check if schedule session button exists (it might not be visible if no accepted requests)
    const buttonExists = await scheduleButton.count() > 0;
    
    if (buttonExists) {
      await expect(scheduleButton).toBeVisible();
      console.log('Schedule session button found');
    } else {
      console.log('No schedule session button found - likely no accepted requests');
    }
  });

  test('should display sessions correctly', async ({ page }) => {
    await page.goto('http://localhost:5174/dashboard/mentorship');
    
    // Check the sessions tab
    const sessionsTab = page.locator('text=Sessions');
    if (await sessionsTab.isVisible()) {
      await sessionsTab.click();
      
      // Check if sessions are displayed or empty state is shown
      const emptyState = page.locator('text=No sessions scheduled');
      const sessionsList = page.locator('.session-card');
      
      const hasEmptyState = await emptyState.isVisible();
      const hasSessions = await sessionsList.count() > 0;
      
      if (hasEmptyState) {
        console.log('No sessions found - empty state displayed');
      } else if (hasSessions) {
        console.log('Sessions found and displayed');
      }
    }
  });

  test('should show create event button for mentors', async ({ page }) => {
    await page.goto('http://localhost:5174/dashboard/mentorship');
    
    // Look for create event button
    const createEventButton = page.locator('text=CREATE EVENT');
    
    const buttonExists = await createEventButton.count() > 0;
    
    if (buttonExists) {
      await expect(createEventButton).toBeVisible();
      console.log('Create event button found');
      
      // Try clicking it to see if dialog opens
      await createEventButton.click();
      
      // Check if dialog opens
      const dialog = page.locator('[role="dialog"]');
      const dialogVisible = await dialog.isVisible();
      
      if (dialogVisible) {
        console.log('Create event dialog opened successfully');
        
        // Check if form fields are present
        await expect(page.locator('text=Create Mentorship Event')).toBeVisible();
        
        // Close dialog
        const closeButton = page.locator('button[icon="close"]');
        if (await closeButton.isVisible()) {
          await closeButton.click();
        }
      }
    } else {
      console.log('No create event button found - user might not be a mentor');
    }
  });

  test('should handle events tab correctly', async ({ page }) => {
    await page.goto('http://localhost:5174/dashboard/mentorship');
    
    // Click on events tab
    const eventsTab = page.locator('text=Events');
    if (await eventsTab.isVisible()) {
      await eventsTab.click();
      
      // Check if events are displayed or empty state is shown
      const emptyState = page.locator('text=No upcoming events');
      const eventsList = page.locator('.event-card');
      
      const hasEmptyState = await emptyState.isVisible();
      const hasEvents = await eventsList.count() > 0;
      
      if (hasEmptyState) {
        console.log('No events found - empty state displayed');
      } else if (hasEvents) {
        console.log('Events found and displayed');
        
        // Check if event cards have proper actions
        const registerButton = page.locator('text=Register');
        const manageButton = page.locator('text=Manage');
        
        if (await registerButton.count() > 0) {
          console.log('Register buttons found on events');
        }
        
        if (await manageButton.count() > 0) {
          console.log('Manage buttons found on events');
        }
      }
    }
  });
});
